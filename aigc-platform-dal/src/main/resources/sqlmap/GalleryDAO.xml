<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.GalleryDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.GalleryDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="sub_type" jdbcType="VARCHAR" property="subType" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="belong" jdbcType="VARCHAR" property="belong" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.GalleryDO">
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    gallery.id, gallery.user_id, gallery.operator_id, gallery.type, gallery.sub_type, gallery.image_url, gallery.md5, gallery.belong, gallery.create_time, gallery.modify_time,
    gallery.deleted
  </sql>
  <sql id="Blob_Column_List">
    gallery.ext_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.GalleryExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gallery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectFavoredByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.GalleryExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gallery
    inner join user_favor on gallery.id = user_favor.item_id
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>

  <select id="selectUsedByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.GalleryExample" resultMap="ResultMapWithBLOBs">
    select
        ranked.*
    from (
      select
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
      ,
      creative_batch_elements.modify_time as use_time
      ,
      ROW_NUMBER() OVER ( PARTITION BY gallery.id ORDER BY creative_batch_elements.modify_time DESC ) AS row_num

      from gallery
      inner join creative_batch_elements on gallery.id = creative_batch_elements.element_id
      <if test="_parameter != null">
        <include refid="Example_Where_Clause" />
      </if>
    ) as ranked
    where ranked.row_num = 1
    order by ranked.use_time desc
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>

  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.GalleryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gallery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gallery
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gallery
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gallery
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.GalleryDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gallery (user_id, operator_id, type, 
      sub_type, image_url, md5, 
      belong, create_time, modify_time, 
      deleted, ext_info)
    values (#{userId,jdbcType=INTEGER}, #{operatorId,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR}, 
      #{subType,jdbcType=VARCHAR}, #{imageUrl,jdbcType=VARCHAR}, #{md5,jdbcType=VARCHAR}, 
      #{belong,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT}, #{extInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.GalleryDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gallery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="belong != null">
        belong,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="belong != null">
        #{belong,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.GalleryExample" resultType="java.lang.Long">
    select count(*) from gallery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="countFavoredByExample" resultType="java.lang.Long">
    select count(*) from gallery
    inner join user_favor on gallery.id = user_favor.item_id
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="countUsedByExample" resultType="java.lang.Long">
    select count(*)
    from (
      select gallery.id from gallery
      inner join creative_batch_elements on gallery.id = creative_batch_elements.element_id
      <if test="_parameter != null">
        <include refid="Example_Where_Clause"/>
      </if>
      group by gallery.id
    ) as distinct_gallery
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    update gallery
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.subType != null">
        sub_type = #{record.subType,jdbcType=VARCHAR},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.belong != null">
        belong = #{record.belong,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update gallery
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      sub_type = #{record.subType,jdbcType=VARCHAR},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      md5 = #{record.md5,jdbcType=VARCHAR},
      belong = #{record.belong,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gallery
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      sub_type = #{record.subType,jdbcType=VARCHAR},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      md5 = #{record.md5,jdbcType=VARCHAR},
      belong = #{record.belong,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.GalleryDO">
    update gallery
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="belong != null">
        belong = #{belong,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.GalleryDO">
    update gallery
    set user_id = #{userId,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      sub_type = #{subType,jdbcType=VARCHAR},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      belong = #{belong,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.GalleryDO">
    update gallery
    set user_id = #{userId,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      sub_type = #{subType,jdbcType=VARCHAR},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      belong = #{belong,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update gallery set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update gallery set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
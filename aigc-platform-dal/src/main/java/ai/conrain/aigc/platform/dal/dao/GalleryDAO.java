package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.GalleryDO;
import ai.conrain.aigc.platform.dal.example.GalleryExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GalleryDAO {
    long countByExample(GalleryExample example);

    long countFavoredByExample(GalleryExample example);

    long countUsedByExample(GalleryExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(GalleryDO record);

    int insertSelective(GalleryDO record);

    List<GalleryDO> selectByExampleWithBLOBs(GalleryExample example);

    List<GalleryDO> selectFavoredByExampleWithBLOBs(GalleryExample example);

    List<GalleryDO> selectUsedByExampleWithBLOBs(GalleryExample example);

    List<GalleryDO> selectByExample(GalleryExample example);

    GalleryDO selectByPrimaryKey(Integer id);

    GalleryDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") GalleryDO record, @Param("example") GalleryExample example);

    int updateByExampleWithBLOBs(@Param("record") GalleryDO record, @Param("example") GalleryExample example);

    int updateByExample(@Param("record") GalleryDO record, @Param("example") GalleryExample example);

    int updateByPrimaryKeySelective(GalleryDO record);

    int updateByPrimaryKeyWithBLOBs(GalleryDO record);

    int updateByPrimaryKey(GalleryDO record);

    int logicalDeleteByExample(@Param("example") GalleryExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}
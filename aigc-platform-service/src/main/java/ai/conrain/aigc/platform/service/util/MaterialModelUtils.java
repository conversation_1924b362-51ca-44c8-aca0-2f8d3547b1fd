/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption.Bottom;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption.Clothing;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption.Top;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.model.biz.ClothAngleDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.LoraTaskParams;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.biz.StyleScene.OutfitDetail;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_MODIFY_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NOW_SHOW_FACE_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.lowerGarment;

/**
 * 服装模型工具类
 *
 * <AUTHOR>
 * @version : MaterialModelUtils.java, v 0.1 2024/12/31 23:21 renxiao.wu Exp $
 */
@Slf4j
public abstract class MaterialModelUtils {
    /**
     * 获取所有镜头角度配对
     *
     * @param model 服装模型
     * @return 所有的镜头角度
     */
    public static List<List<CameraAngleEnum>> fetchAllCameraAngle(MaterialModelVO model) {
        List<List<CameraAngleEnum>> result = new ArrayList<>();
        if (model == null) {
            return result;
        }
        List<ClothTypeConfig> configs = MaterialModelConverter.convert2ClothTypeConfig(model);
        if (CollectionUtils.isEmpty(configs)) {
            return result;
        }

        for (ClothTypeConfig config : configs) {
            List<ClothColorDetail> filter = config.getColorList().stream().filter(ClothColorDetail::isEnable).collect(
                Collectors.toList());
            if (CollectionUtils.isEmpty(filter)) {
                continue;
            }

            CameraAngleEnum bodyPosition = CameraAngleEnum.getBodyPositionByStr(config.getType());
            CameraAngleEnum orientation = CameraAngleEnum.getOrientationByStr(config.getType());

            if (result.stream().noneMatch(list -> list.contains(bodyPosition) && list.contains(orientation))) {
                result.add(Arrays.asList(bodyPosition, orientation));
            }
        }

        return result;
    }

    /**
     * 获取所有镜头角度配对
     *
     * @param model 服装模型
     * @return 所有的镜头角度
     */
    public static List<List<String>> fetchAllCameraAngleCode(MaterialModelVO model) {
        List<List<CameraAngleEnum>> lists = fetchAllCameraAngle(model);
        if (CollectionUtils.isEmpty(lists)) {
            return new ArrayList<>();
        }

        return lists.stream().map(list -> list.stream().map(CameraAngleEnum::getCode).collect(Collectors.toList()))
            .collect(Collectors.toList());
    }

    /**
     * 是否已经上传lora文件到oss
     *
     * @param detail lora训练详情
     * @return true，已上传
     */
    public static boolean alreadyUploadedLora(LoraTrainDetail detail) {
        AssertUtil.assertNotNull(detail, "lora训练详情为空: " + detail);

        String ossUrl = detail.getLoraRetFileUrl();
        if (StringUtils.isBlank(ossUrl)) {
            return false;
        }

        // ossUrl类似于https://aigc-platform-online.oss-cn-zhangjiakou.aliyuncs.com/lora/20241220/020816_1395_%E9%A3%8E%E6%A0%BC-%E5%B3%B0%E8%B6%8A%E5%A6%88%E5%A6%88_4876_20241219_225134-flux.safetensors?Expires=3311431702&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=d%2BJMbnroWxx9jel5Kl1JQfFWPTg%3D
        // 需要取其中lora/后面的八位字符串转换成日期
        int startIdx = ossUrl.indexOf(".com/lora/") + 10;
        String dateStr = ossUrl.substring(startIdx, startIdx + 8);
        Date storeDate = DateUtils.parseShortLastTime(dateStr);
        Date confirmDate = DateUtils.parseSimple(detail.getLoraConfirmedTime());

        boolean uploaded = DateUtils.compare(storeDate, confirmDate) > 0;
        if (!uploaded) {
            log.info("【Lora上传oss事件】oss上存在较老版本的lora，重新上传,ossUrl={}", ossUrl);
        }
        return uploaded;
    }

    /**
     * 构建训练的扩展信息
     *
     * @param materialType 素材类型
     * @param systemConfig 系统配置
     * @return 扩展信息
     */
    public static String buildTrainExtInfo(MaterialType materialType, JSONObject systemConfig) {
        if (systemConfig == null || systemConfig.isEmpty()) {
            return "";
        }

        JSONObject jsonObject = systemConfig.getJSONObject(materialType.name());
        return buildTrainExtInfo(jsonObject);
    }

    /**
     * 构建训练的扩展信息
     *
     * @param materialType 素材类型
     * @param systemConfig 系统配置
     * @return 扩展信息
     */
    public static String buildTrainExtInfo(String materialType, JSONObject systemConfig) {
        return buildTrainExtInfo(MaterialType.valueOf(materialType), systemConfig);
    }

    /**
     * 构建训练的扩展信息
     *
     * @param extInfo 扩展信息
     * @return 训练扩展信息字符串
     */
    public static String buildTrainExtInfo(JSONObject extInfo) {
        if (MapUtils.isEmpty(extInfo)) {
            return "";
        }
        return extInfo.toJSONString();
    }

    /**
     * 格式化场景标签内容
     *
     * @param labelContentJson 标签内容json
     * @return 场景标签内容
     */
    public static String formatSceneLabelContent(JSONObject labelContentJson) {
        List<String> result = new ArrayList<>();
        Object activateKey = labelContentJson.getJSONObject("desc").remove("activateKey");
        CommonUtil.getJsonValue(labelContentJson, result);
        return activateKey + String.join("", result);
    }

    /**
     * 构建打标子路径
     *
     * @param rootPath    跟路径
     * @param labelType   打标类型
     * @param repeatTimes 重复训练次数
     * @return 打标子路径
     */
    public static String buildLabelSubPath(String rootPath, String labelType, Integer repeatTimes) {
        return rootPath + "/" + repeatTimes + "_" + labelType;
    }

    /**
     * 构建测试出图角度列表
     *
     * @param isLowerBody 是否
     * @return 出图角度列表
     */
    public static List<List<String>> buildTestCameraAngles(boolean isLowerBody) {
        CameraAngleEnum halfBody = isLowerBody ? CameraAngleEnum.LOWER_BODY : CameraAngleEnum.UPPER_BODY;

        return Arrays.asList(Arrays.asList(CameraAngleEnum.WHOLE_BODY.getCode(), CameraAngleEnum.FRONT_VIEW.getCode()),
            Arrays.asList(halfBody.getCode(), CameraAngleEnum.FRONT_VIEW.getCode()),
            Arrays.asList(CameraAngleEnum.WHOLE_BODY.getCode(), CameraAngleEnum.BACK_VIEW.getCode()));
    }

    /**
     * 解析服装拍摄角度图片详情
     *
     * @param content 解析文件内容
     * @return 服装拍摄角度图片详情
     */
    public static ClothAngleDetail parseFromPreview(String content) {
        ClothAngleDetail detail = new ClothAngleDetail();

        if (StringUtils.isBlank(content)) {
            return detail;
        }
        String[] lines = content.split("\n");

        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            if (StringUtils.isBlank(detail.getFrontFullImg())) {
                detail.setFrontFullImg(parseMainFile(line, "front", true));
            }
            if (StringUtils.isBlank(detail.getBackFullImg())) {
                detail.setBackFullImg(parseMainFile(line, "back", false));
            }
            if (StringUtils.isBlank(detail.getFrontHalfImg())) {
                detail.setFrontHalfImg(parseMainFile(line, "front", false));
            }
            if (StringUtils.isBlank(detail.getBackHalfImg())) {
                detail.setBackHalfImg(parseMainFile(line, "back", false));
            }
        }

        return detail;
    }

    /**
     * 初始化附加信息
     *
     * @param detail 训练明细
     */
    public static void initAppendInfo(LoraTrainDetail detail) {
        if (!StringUtils.equals(YES, detail.getNoshowFace())) {
            return;
        }
        String appendInfo = "{\"scene.composition\": \"." + NOW_SHOW_FACE_PROMPT + "\"}";
        appendInfo = CommonUtil.unescapeLineBreak(appendInfo);
        appendInfo = ComfyUIUtils.parseParams(appendInfo);
        detail.setAppendInfo(appendInfo);
    }

    /**
     * 填充打标源目录
     *
     * @param trainDetail 训练明细
     */
    public static void fillLabelSourceDir(LoraTrainDetail trainDetail) {
        //目前只给场景在用
        if (!StringUtils.equals(trainDetail.getMaterialType(), MaterialType.scene.name())) {
            return;
        }
        String labelSourceDir = trainDetail.getClothDir();
        if (StringUtils.equals(trainDetail.getPreprocessCensoredFace(), CommonConstants.YES)) {
            labelSourceDir = trainDetail.getPrepareViewRetDir();
        }
        if (StringUtils.isNotBlank(trainDetail.getWaterMarkDesc()) || StringUtils.isNotBlank(
            trainDetail.getWatermarkOrigin())) {
            labelSourceDir = trainDetail.getCutoutRetDir();
        }

        trainDetail.setLabelSourceDir(labelSourceDir);
    }

    /**
     * 填充裁剪源目录
     *
     * @param trainDetail 训练明细
     */
    public static void fillCutoutSourceDir(LoraTrainDetail trainDetail) {
        //目前只给场景在用
        if (!StringUtils.equals(trainDetail.getMaterialType(), MaterialType.scene.name())) {
            return;
        }
        String cutoutSourceDir = trainDetail.getClothDir();
        //如果白头处理，则使用views目录，否则是原图目录
        if (StringUtils.equals(trainDetail.getPreprocessCensoredFace(), CommonConstants.YES)) {
            cutoutSourceDir = trainDetail.getPrepareViewRetDir();
        }

        trainDetail.setCutoutSourceDir(cutoutSourceDir);
    }

    /**
     * 是否修改提示词
     *
     * @param origin 原始模型
     * @param target 目标模型
     * @return 是否修改提示词, true已修改
     */
    public static boolean isModifyPrompt(MaterialModelVO origin, MaterialModelVO target) {
        if (origin == null || target == null) {
            return false;
        }

        if (StringUtils.equals(YES, origin.getExtInfo(KEY_IS_MODIFY_PROMPT, String.class))) {
            log.info("【是否修改提示词】当前模型已记录修改过提示词,直接跳过, id:{}", origin.getId());
            return false;
        }

        List<ClothTypeConfig> originConfigs = origin.getClothTypeConfigs();
        List<ClothTypeConfig> targetConfigs = target.getClothTypeConfigs();
        if (CollectionUtils.isEmpty(targetConfigs) || CollectionUtils.isEmpty(originConfigs)) {
            return false;
        }

        // 对originConfigs和targetConfigs按type进行重新排序
        originConfigs = sortClothTypeConfigs(originConfigs);
        targetConfigs = sortClothTypeConfigs(targetConfigs);

        if (CollectionUtils.size(originConfigs) != CollectionUtils.size(targetConfigs)) {
            log.info("【是否修改提示词】服装配置数量不一致，修改提示词=true, id:{}", origin.getId());
            return true;
        }

        for (int i = 0; i < targetConfigs.size(); i++) {
            ClothTypeConfig targetEach = targetConfigs.get(i);
            ClothTypeConfig originEach = originConfigs.get(i);

            List<ClothColorDetail> targetColorList = targetEach.getColorList();
            targetColorList.sort(Comparator.comparing(ClothColorDetail::getIndex));
            List<ClothColorDetail> originColorList = originEach.getColorList();
            originColorList.sort(Comparator.comparing(ClothColorDetail::getIndex));

            if (CollectionUtils.size(targetColorList) != CollectionUtils.size(originColorList)) {
                log.info("【是否修改提示词】颜色数量不一致，修改提示词=true, id:{},idx={}", origin.getId(), i);
                return true;
            }

            for (int j = 0; j < targetColorList.size(); j++) {
                ClothColorDetail targetColor = targetColorList.get(j);
                ClothColorDetail originColor = originColorList.get(j);

                if (!StringUtils.equals(targetColor.getValue(), originColor.getValue())) {
                    log.info("【是否修改提示词】prompt不一致，修改提示词=true, id:{},idx={},colorIdx={}", origin.getId(),
                        i, j);
                    return true;
                }
            }
        }

        log.info("【是否修改提示词】prompt一致,修改提示词=false, id:{}", origin.getId());
        return false;
    }

    /**
     * 对 ClothTypeConfig 列表按 getType() 排序
     *
     * @param configs 需要排序的配置列表
     * @return 排序后的列表
     */
    public static List<ClothTypeConfig> sortClothTypeConfigs(List<ClothTypeConfig> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return configs;
        }

        return configs.stream().sorted(Comparator.comparing(config -> {
            List<String> types = new ArrayList<>(config.getType());
            Collections.sort(types);
            return types;
        }, (list1, list2) -> {
            for (int i = 0; i < Math.min(list1.size(), list2.size()); i++) {
                int cmp = list1.get(i).compareTo(list2.get(i));
                if (cmp != 0) {return cmp;}
            }
            return Integer.compare(list1.size(), list2.size());
        })).collect(Collectors.toList());
    }

    /**
     * 是否打标完成但未确认
     *
     * @param model 模型
     * @return true，打标完成但未确认
     */
    public static boolean isLabelFinishedNotConfirmed(MaterialModelVO model) {
        return model.getClothLoraTrainDetail() != null && model.getClothLoraTrainDetail().getLabel() != null
               && QueueResult.QueueCodeEnum.COMPLETED == model.getClothLoraTrainDetail().getLabel().getStatus()
               && !StringUtils.equals(model.getClothLoraTrainDetail().getLoraConfirmed(), CommonConstants.YES);
    }

    /**
     * 从预览中解析服装类目
     *
     * @param content 预览内容
     * @return 服装类目
     */
    public static String parseGarmentFromPreview(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }

        String[] lines = content.split("\n");
        Map<String, Integer> garmentCountMap = new HashMap<>();

        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            String detailContent = StringUtils.substringAfter(line, ":");
            if (!CommonUtil.isValidJson(detailContent)) {
                continue;
            }

            JSONObject detail = JSONObject.parseObject(detailContent);
            if (detail.containsKey("garment_category")) {
                String garmentCategory = detail.getString("garment_category");
                if (StringUtils.isNotBlank(garmentCategory)) {
                    garmentCountMap.put(garmentCategory, garmentCountMap.getOrDefault(garmentCategory, 0) + 1);
                }
            }
        }

        // 使用投票机制，选择出现次数最多的garment_category
        if (!garmentCountMap.isEmpty()) {
            return garmentCountMap.entrySet().stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey).orElse(
                null);
        }

        return null;
    }

    /**
     * 构建服装颜色详情
     *
     * @param task      训练任务
     * @param angleList 角度列表
     * @param json      获取到的json数据
     * @return 服装颜色详情
     */
    public static ClothColorDetail buildClothColorDetail(ComfyuiTaskVO task, List<CameraAngleEnum> angleList,
                                                         JSONObject json) {
        ImageAnalysisCaption analysis = JSONObject.toJavaObject(json.getJSONObject("analysis"),
            ImageAnalysisCaption.class);
        String prompt = json.getString("prompt");
        LoraTaskParams params = task.getReqParams().toJavaObject(LoraTaskParams.class);
        CameraAngleEnum bodyPosition = CameraAngleEnum.getBodyPosition(angleList);

        String extTags = formatExtTags(prompt, bodyPosition);
        int index = Integer.parseInt(params.getColorNumber());

        ClothColorDetail detail = null;
        OutfitDetail outfitDetail = buildOutfitDetail(analysis);

        String views = JSONObject.toJSONString(outfitDetail);
        if (Objects.equals(params.getClothType(), lowerGarment)) {
            Bottom bottom = analysis.getClothing().getBottom();
            detail = new ClothColorDetail(index, bottom.getColor(), extTags, bottom.getType(), bodyPosition.getExtTag(),
                views, JSONObject.toJSONString(analysis));
        } else {
            Top top = analysis.getClothing().getTop();

            detail = new ClothColorDetail(index, top.getColor(), extTags, top.getType(), bodyPosition.getExtTag(),
                views, JSONObject.toJSONString(analysis));
        }
        //else if (Objects.equals(params.getClothType(), outfit)) {}

        return detail;
    }

    /**
     * 通过分析结果构建服装详情
     *
     * @param analysis 图片分析结果
     * @return 服装详情
     */
    private static OutfitDetail buildOutfitDetail(ImageAnalysisCaption analysis) {
        OutfitDetail detail = new OutfitDetail();
        Clothing clothing = analysis.getClothing();
        Top top = clothing.getTop();

        if (top != null && StringUtils.isNotBlank(top.getType())) {
            detail.setOuterwear(String.format("a %s %s", top.getColor(), top.getType()));
        }
        Bottom bottom = clothing.getBottom();
        if (bottom != null && StringUtils.isNotBlank(bottom.getType())) {
            detail.setBottoms(String.format("a %s %s", bottom.getColor(), bottom.getType()));
        }
        //TODO by半泉: 内搭逻辑还需要等待确认
        detail.setShoes(clothing.getShoes());
        if (StringUtils.isBlank(clothing.getAccessories())) {
            detail.setAccessories(Collections.singletonList(clothing.getAccessories()));
        }
        return detail;
    }

    /**
     * 构建扩展标签
     * 补充激活词: The model is wearing A blue varsity jacket with a round collar and white long
     * sleeves, reaching the hips. The jacket has two pockets.
     *
     * @param extTags      扩展标签
     * @param bodyPosition 身体位置
     * @return format后的扩展标签
     */
    public static String formatExtTags(String extTags, CameraAngleEnum bodyPosition) {
        if (StringUtils.isBlank(extTags)) {
            return extTags;
        }

        String prefix = bodyPosition.getExtTag();

        extTags = prefix + CommonUtil.replaceIgnoreCase(extTags, "dark", "deep");
        extTags = ComfyUIUtils.appendDotIfNeeded(extTags);
        return extTags;
    }

    /**
     * 是否外套
     *
     * @param model 服装模型
     * @return true, 外套
     */
    public static boolean isCoat(MaterialModelVO model) {
        if (null == model) {
            return false;
        }
        String aiGenFeaturesJson = model.getExtInfo(CommonConstants.aiGenFeatures, String.class);

        if (CommonUtil.isValidJson(aiGenFeaturesJson) && !JSONObject.parseObject(aiGenFeaturesJson).isEmpty()) {
            ClothCollocationModel aiGenFeatures = JSONObject.parseObject(aiGenFeaturesJson,
                ClothCollocationModel.class);
            return aiGenFeatures != null && StringUtils.isNotBlank(aiGenFeatures.getTops());
        }

        return false;
    }

    /**
     * 解析主文件
     *
     * @param content  内容
     * @param viewType 视角类型
     * @param isFull   是否全身
     * @return 主文件
     */
    private static String parseMainFile(String content, String viewType, boolean isFull) {
        String imageName = StringUtils.substringBefore(content, ":");
        String imageContent = StringUtils.substringAfter(content, ":");
        if (StringUtils.isBlank(imageName) || StringUtils.isBlank(imageContent)) {
            return null;
        }
        //取半身的图片
        if (!StringUtils.contains(imageName, "_c1") || (!isFull && StringUtils.contains(imageName, "_full_"))) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(imageContent);
        if (StringUtils.equals(jsonObject.getString("view_type"), viewType)) {
            return imageName;
        }
        return null;
    }
}

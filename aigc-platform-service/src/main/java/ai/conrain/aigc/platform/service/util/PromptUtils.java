/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * prompt工具类
 *
 * <AUTHOR>
 * @version : PromptUtils.java, v 0.1 2024/9/21 11:38 renxiao.wu Exp $
 */
public abstract class PromptUtils {
    private static final Map<String, String> CLOTH_TYPE_MAP = new HashMap<>();

    static {
        CLOTH_TYPE_MAP.put("outfit", "full set");
        CLOTH_TYPE_MAP.put("upper garment", "upper body");
        CLOTH_TYPE_MAP.put("lower garment", "lower body");
    }

    /**
     * 基于seed对带{|}的prompt进行随机匹配
     *
     * @param prompts 原始prompt
     * @param seed    种子
     * @return 结果
     */
    public static String random(String prompts, long seed) {
        if (StringUtils.isBlank(prompts)) {
            return prompts;
        }

        // 设置随机种子，种子在4096内random.nextInt(2)都是100%生成1，所以这里先去掉
        Random random = new Random(seed);

        // 处理多层嵌套的随机选择
        return parseTemplate(prompts, random);
    }

    /**
     * 基于seed对在1-max之间进行随机
     *
     * @param max  最大值
     * @param seed 种子
     * @return 结果
     */
    public static int random(int max, long seed) {
        if (max == 1) {
            return max;
        }

        Random random = new Random(seed);

        // 处理多层嵌套的随机选择
        return random.nextInt(max) + 1;
    }

    /**
     * 构建服装复杂度咨询prompt
     *
     * @param template  模板
     * @param clothType 服装类型
     * @return 咨询prompt
     */
    public static String buildClothComplexPrompt(String template, String clothType) {
        if (StringUtils.isBlank(template)) {
            return null;
        }
        return String.format(template, CLOTH_TYPE_MAP.get(clothType));
    }

    /**
     * 从workflow中获取prompt
     *
     * @param workflow 工作流
     * @return prompt
     */
    public static String fetchPromptFromWorkflow(JSONObject workflow) {
        if (workflow == null) {
            return null;
        }
        return workflow.getJSONObject("prompt").getJSONObject("282").getJSONObject("inputs").getString("prompts");
    }

    /**
     * 递归解析模板
     *
     * @param template 模板
     * @param random   随机
     * @return 解析结果
     */
    private static String parseTemplate(String template, Random random) {
        Pattern pattern = Pattern.compile("\\{([^{}]*)}");
        Matcher matcher = pattern.matcher(template);

        while (matcher.find()) {
            String[] options = matcher.group(1).split("\\|");
            String selected = options[random.nextInt(options.length)];
            // 递归处理嵌套结构
            selected = parseTemplate(selected, random);
            template = template.substring(0, matcher.start()) + selected + template.substring(matcher.end());
            matcher = pattern.matcher(template);
        }
        return template;
    }

}

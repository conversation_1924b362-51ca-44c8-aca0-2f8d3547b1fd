package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ImageExpandRequest implements CreativeRequest{
    private static final long serialVersionUID = 1L;

    /** 扩展方向：上up/下down/左left/右right/全部all */
    private String expandDirection;

    /** 扩展像素大小 */
    private Integer expandPixels;

    /** 出图数量 */
    @NotNull
    private Integer imageNum;

    /** 图片来源：上传upload/历史纪录history */
    @NotBlank
    private String imageSource;

    /** 图片地址 */
    @NotBlank
    private String imageUrl;

    /** 服装图片名字 */
    //@NotBlank
    //private String originImageName;

    /** 目标尺寸 */
    @NotBlank
    private String targetSize;

    /** 原图在结果图中的位置 */
    private String originPosition;
}

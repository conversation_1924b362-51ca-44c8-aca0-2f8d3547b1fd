package ai.conrain.aigc.platform.service.component.creative;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.util.StringUtils;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.request.ImageExpandRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class ImageExpandService extends AbstractCreativeService<ImageExpandRequest> {
    @Value("${comfyui.input.path}")
    private String inputPath;
    @Autowired
    private ComfyUIHelper comfyUIHelper;


    // 构建批次信息
    @Override
    protected CreativeBatchVO buildData(ImageExpandRequest request, MaterialModelVO modelVO) throws IOException {
        CreativeBatchVO creativeBatchVO = new CreativeBatchVO();
        // 设置类型
        creativeBatchVO.setType(getCreativeType());
        // 设置用户id
        creativeBatchVO.setUserId(OperationContextHolder.getMasterUserId());
        // 设置操作者id
        creativeBatchVO.setOperatorId(OperationContextHolder.getOperatorUserId());
        // 设置展示图片
        creativeBatchVO.setShowImage(request.getImageUrl());
        // 批次数量，就是扩图数量，直接影响扣费
        creativeBatchVO.setBatchCnt(request.getImageNum());
        // 设置状态(排队中)
        creativeBatchVO.setStatus(CreativeStatusEnum.QUEUE);
        // 额外信息中添加原图路径
        creativeBatchVO.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE,request.getImageUrl());
        // 图像比例
        creativeBatchVO.setImageProportion(CommonConstants.NONE);
        // 设置用户id
        creativeBatchVO.setUserId(OperationContextHolder.getMasterUserId());
        // 设置操作者id
        creativeBatchVO.setOperatorId(OperationContextHolder.getOperatorUserId());
        // 添加扩图参数(待定）
        creativeBatchVO.addExtInfo("expandDirection",request.getExpandDirection());
        creativeBatchVO.addExtInfo("expandPixels",request.getExpandPixels());
        // 目标尺寸
        creativeBatchVO.addExtInfo(CommonConstants.TARGET_SIZE,request.getTargetSize());
        try {
            // 上传图片至 ComfyUI
            String upLoadedImgFilePath = comfyUIHelper.upLoadImage(request.getImageUrl());
            // 添加原图名字，从comfyUi路径中提取
            creativeBatchVO.addExtInfo(CommonConstants.TARGET_IMAGE, upLoadedImgFilePath);
            // 配置输出路径
            String outputPath = upLoadedImgFilePath;
            if (StringUtils.isNotBlank(outputPath) && outputPath.contains("/")) {
                outputPath = outputPath.substring(0, outputPath.lastIndexOf("/"));
            }
            creativeBatchVO.addExtInfo(CommonConstants.OUT_PUT_PATH, outputPath);
            // 文件名前缀
            String fileNamePrefix = "";
            String fileName = "";
            if (StringUtils.isNotBlank(upLoadedImgFilePath) && outputPath.contains("/")) {
                fileName = upLoadedImgFilePath.substring(upLoadedImgFilePath.lastIndexOf("/") + 1);
            }
            if (fileName.contains(".")) {
                fileNamePrefix = fileName.substring(0, fileName.lastIndexOf("."));
            }else {
                fileNamePrefix = fileName;
            }
            creativeBatchVO.addExtInfo(CommonConstants.FILE_NAME_PREFIX,fileNamePrefix);
        } catch (Exception e) {
            log.error("图片上传至ComfyUI出现异常", e);
        }

        return creativeBatchVO;
    }

    // 返回实例的类型
    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.IMAGE_EXPAND;
    }

    // 填充creativeTask中的扩展信息
    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        // 设置批次创作数量
        target.setBatchCnt(batch.getBatchCnt());
        // 目标尺寸
        target.addExtInfo(CommonConstants.TARGET_SIZE,batch.getStringFromExtInfo(CommonConstants.TARGET_SIZE));
        // 原图文件名
        target.addExtInfo(CommonConstants.TARGET_IMAGE,batch.getStringFromExtInfo(CommonConstants.TARGET_IMAGE));
        // 输出路径
        target.addExtInfo(CommonConstants.OUT_PUT_PATH,batch.getStringFromExtInfo(CommonConstants.OUT_PUT_PATH));
        // 文件名前缀
        target.addExtInfo(CommonConstants.FILE_NAME_PREFIX,batch.getStringFromExtInfo(CommonConstants.FILE_NAME_PREFIX));
    }

    // 填充工作流的参数
    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        // 先调用一次父类（空的）
        super.preParse(task, elements, modelVO, context);
        // 配置扩图特有参数
        // 扩图图片
        //context.put("expandImage", task.getStringFromExtInfo(CommonConstants.KEY_ORIGIN_IMAGE));
        //// 扩展方向
        //context.put("expandDirection", task.getStringFromExtInfo("expandDirection"));
        //// 扩展像素
        //context.put("expandPixels", task.getStringFromExtInfo("expandPixels"));
        // 高分辨率扣费
        // 原图名
        context.put(CommonConstants.TARGET_IMAGE, task.getStringFromExtInfo(CommonConstants.TARGET_IMAGE));
        // 目标尺寸
        context.put(CommonConstants.TARGET_SIZE, task.getStringFromExtInfo(CommonConstants.TARGET_SIZE));
        // 图片位置
        context.put(CommonConstants.ORIGIN_POSITION,task.getStringFromExtInfo(CommonConstants.ORIGIN_POSITION));
        // 输出路径
        context.put(CommonConstants.OUT_PUT_PATH, task.getStringFromExtInfo(CommonConstants.OUT_PUT_PATH));
        // 文件名前缀
        context.put(CommonConstants.FILE_NAME_PREFIX, task.getStringFromExtInfo(CommonConstants.FILE_NAME_PREFIX));
    }
}

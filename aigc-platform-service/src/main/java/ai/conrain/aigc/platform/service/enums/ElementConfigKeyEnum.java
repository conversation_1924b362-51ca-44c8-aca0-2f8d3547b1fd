package ai.conrain.aigc.platform.service.enums;

import ai.conrain.aigc.platform.service.constants.SystemConstants;

public enum ElementConfigKeyEnum {
    FACE, SCENE, CLOTH_STYLE, REFER, LOGO_POSITION, BRAND_IMAGE, GALLERY;

    public static String getSystemConfigKey(String key) {
        switch (ElementConfigKeyEnum.valueOf(key)) {
            case FACE:
                return SystemConstants.FACE_TYPE_CFG;
            case SCENE:
                return SystemConstants.SCENE_TYPE_CFG;
            case CLOTH_STYLE:
                return SystemConstants.CLOTH_STYLE_TYPE_CFG;
            case REFER:
                return SystemConstants.REFER_TYPE_CFG;
            case LOGO_POSITION:
                return SystemConstants.LOGO_LOCATION_TYPE_CFG;
            case BRAND_IMAGE:
                return SystemConstants.BRAND_IMAGE_TYPE_CFG;
            default:
                throw new RuntimeException("不支持的元素配置key:" + key);
        }
    }
}

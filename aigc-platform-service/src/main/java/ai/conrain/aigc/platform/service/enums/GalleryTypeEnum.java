package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum GalleryTypeEnum {

    BRAND_IMAGE("BRAND_IMAGE", "大牌上身商品图"),

    BRAND_UPLOAD("BRAND_UPLOAD", "大牌上身用户上传"),

    REFERENCE_UPLOAD("REFERENCE_UPLOAD", "用户上传参考图"),
    ;

    private final String code;

    private final String desc;

    public static GalleryTypeEnum getByCode(String code) {
        for (GalleryTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    GalleryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
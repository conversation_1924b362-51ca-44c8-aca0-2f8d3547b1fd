/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.constants;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.regex.Pattern;

/**
 * 通用常量
 *
 * <AUTHOR>
 * @version : CommonConstants.java, v 0.1 2023/9/3 18:25 renxiao.wu Exp $
 */
public interface CommonConstants {

    /** 会话key：session_id */
    String SESSION_KEY_ID = "cr_session_id";

    /** 会话前缀 */
    String SESSION_PREFIX = "session_";

    /** tairkey值前缀 */
    String TAIR_KEY_PREFIX = "tair_";

    /** salt值前缀 */
    String SALT_KEY_PREFIX = "salt_";

    /** 验证码 */
    String CAPTCHA_KEY_PREFIX = "tair_captcha_";

    /** 一天的秒数 */
    int ONE_DAY_SECONDS = 24 * 60 * 60;

    /** 14天的秒数 */
    int DAY_14_SECONDS = 14 * 24 * 60 * 60;

    /** pc session过期时间：1天 */
    int PC_SESSION_EXPIRE_TIME = ONE_DAY_SECONDS;

    /** 微信小程序session过期时间：14天 */
    int MINI_APP_SESSION_EXPIRE_TIME = DAY_14_SECONDS;

    /** 验证码过期时间：5分钟 */
    int CAPTCHA_EXPIRE_TIME = 5 * 60;

    /** 支付超时时间：30分钟 */
    int PAY_TIMEOUT_SECOND = 30 * 60;

    /** 本地缓存过期时间：5分钟 */
    long LOCAL_CACHE_EXPIRE_TIME_MILLIS = 5 * 60 * 1000;

    String KEY_MULTIPLY = "x";

    String TAIR_REST_PSWD_MOBLE = "tair_rest_pswd_mobile";

    /** 手机号正则，普通手机号（1[3-9] 开头）或测试手机号（288 开头）的 11 位数字 */
    Pattern MOBILE_PATTERN = Pattern.compile("^(1[3-9]\\d{9}|288\\d{8})$");

    /** Y字符串 */
    String YES = "Y";

    /** N字符串 */
    String NO = "N";

    /** 冒号 */
    String COLON = ":";

    /** 最大密码错误次数 */
    int MAX_PASSWORD_FAIL_CNT = 6;

    /** 最大子账号数量 */
    int MAX_SUB_USER_COUNT = 1000;

    /** 默认最大刷新用户间隔1分钟 */
    long MAX_REFRESH_USER_INTERVAL = 1000 * 60;

    /** 密码正则 */
    Pattern PASSWORD_PATTERN = Pattern.compile("^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d]{8,16}$");

    /** Decimal格式化保留最多2位小数，如无小数则去掉小数，四舍五入 */
    DecimalFormat ROUND_TWO_DECIMAL_FORMAT = new DecimalFormat("#0.##");

    /** Decimal格式化保留两位小数 */
    DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#0.00");

    /** Decimal格式化保留两位小数,带千分位 */
    DecimalFormat COMMA_DECIMAL_FORMAT = new DecimalFormat("#,##0.00");

    /** 斤的Decimal格式化不保留小数 */
    DecimalFormat CATTY_DECIMAL_FORMAT = new DecimalFormat("0");

    /** Decimal格式化保留两位小数的rate */
    DecimalFormat RATE_DECIMAL_FORMAT = new DecimalFormat("#.##%");

    /** 存储NULL */
    String NULL_STR = "_$#CR_NULL#$_";

    String KEY_QUEUE_SIZE = "queueSize";
    String KEY_START_RUN_TIME = "startRunTime";
    String KEY_FINISH_TIME = "finishTime";
    String KEY_MAIN_IMAGES = "mainImages";
    String KEY_GARMENT_CATEGORY = "garmentCategory";
    String KEY_ZIP_URL = "zipUrl";
    String KEY_LATEST_ZIP_TIME = "latestZipTime";

    String KEY_SNAPSHOT_FACE_NAME = "faceNameSnapshot";
    String KEY_SNAPSHOT_SCENE_NAME = "sceneNameSnapshot";

    String KEY_SCHEDULE = "schedule";
    String KEY_START_TIME = "startTime";
    String KEY_END_TIME = "endTime";

    String KEY_TOTAL_NODE = "totalNode";

    String KEY_TRY_TIMES = "tryTimes";

    String KEY_LIKE = "like";

    String KEY_LIKE_STATUS = "LIKE";
    String KEY_DISLIKE_STATUS = "DISLIKE";

    int BUFFER_SIZE = 1024;

    int TEN_MINUTES = 10 * 60;

    /** 最大ComfyUI线程处理数量 */
    long MAX_PROCESS_CNT = 2;

    String KEY_CREATIVE_QUERY_LASTED_TIME = "KEY_CREATIVE_QUERY_LASTED_TIME";

    Pattern OSS_FILE_PATH_PATTERN = Pattern.compile("oss-cn-zhangjiakou\\.aliyuncs\\.com/([^?]+)");

    String KEY_CFG = "cfg";

    double DEFAULT_CFG = 3.5;

    String KEY_FACE_RESTORE_VISIBILITY = "faceRestoreVisibility";
    String KEY_FACE_IMAGE = "faceImage";
    String KEY_FACE_IMAGE_MORE = "faceImageMore";

    /** 细节修补 */
    String KEY_CLOTH_ORIGIN_IMAGE = "clothOriginImage";
    String KEY_GROW_MASK = "growMask";
    String KEY_MERGED_IMAGE_COMFY_UI = "mergedImageComfyUI";
    String KEY_MERGED_IMAGE_OSS = "mergedImageOss";
    String KEY_TASK_INDEX = "taskIndex";
    Integer REPAIR_DETAIL_TASK_NUM = 4;

    String KEY_REPAIR_IMAGE = "repairImage";
    String KEY_LOGO_MODEL_DESC = "logoModelDesc";
    String KEY_REPAIR_IMAGE_OSS = "repairImageOss";
    String KEY_ORIGIN_IMAGE = "originImage";
    String KEY_ORIGIN_MASK_IMAGE = "originMaskImage";
    String KEY_MIDDLE_IMAGE = "middleImage";
    String KEY_ORIGIN_TASK = "originTask";

    /** 使用细节修补流程的大牌上身 */
    String KEY_ORIGIN_IMAGE_MASK = "originImageMask";
    String KEY_CLOTH_IMAGE = "clothImage";
    String KEY_CLOTH_IMAGE_MASK = "clothImageMask";

    String KEY_ORIGIN_IMAGE_COMFY_UI = "originImageComfyUI";
    String KEY_CLOTH_IMAGE_COMFY_UI = "clothImageComfyUI";
    String KEY_ORIGIN_IMAGE_MASK_COMFY_UI = "originImageMaskComfyUI";
    String KEY_CLOTH_IMAGE_MASK_COMFY_UI = "clothImageMaskComfyUI";
    String KEY_REFERENCE_IMAGES = "referenceImages";

    String KEY_ORIGIN_IMAGE_PATH = "originImagePath";
    String KEY_ORIGIN_IMAGE_PREFIX = "originImagePrefix";
    String KEY_TARGET_IMAGE_PATH = "targetImagePath";
    String KEY_RESULT_IMAGE_FORMAT = "resultImageFormat";

    String KEY_MASK_IMAGE_PATH = "maskImagePath";

    String KEY_TARGET_HEX_COLOR = "targetHexColor";
    String KEY_SMART_RECOLOR_MODE = "smartRecolorMode";

    String KEY_ORIGIN_HEIGHT = "originHeight";
    String KEY_ORIGIN_WIDTH = "originWidth";
    String KEY_RESIZE_WIDTH = "resizeWidth";

    String KEY_TIME_SECS_4_VIDEO = "timeSecs4Video";
    String KEY_RESULT_SIZE = "resultSize";
    String KEY_SKIN_TYPE = "skinType";
    String KEY_PROMPT = "prompt";
    String KEY_NEGATIVE_PROMPT = "negativePrompt";

    /** 消除笔V2 */
    String KEY_RESULT_IMAGE_PATH = "resultImagePath";
    String KEY_RESULT_IMAGE_PREFIX = "resultImagePrefix";
    String KEY_CREATIVE_TASK_STACK = "creativeTaskStack";
    String KEY_CUSTOM_STATUS = "customStatus";

    /** 用户上传场景图 */
    String KEY_CUSTOM_SCENE_IMG = "customSceneImg";
    String KEY_ORIGIN_CUSTOM_SCENE_IMG = "originCustomSceneImg";

    String KEY_REDRAW_IMAGE = "redrawImage";
    String KEY_REDRAW_IMAGE_OSS = "redrawImageOss";
    String KEY_REDRAW_DESC = "redrawDesc";

    String KEY_LOGO_IMAGE = "logoImage";
    String KEY_LOGO_IMAGE_OSS = "logoImageOss";
    String KEY_MASK_IMAGE = "maskImage";

    String KEY_REFER_POSE = "referPoseImage";

    String KEY_REFER_IMAGE = "referImage";
    String KEY_REFER_ID = "referId";

    String loraConfirmed = "loraConfirmed";
    String loraConfirmedTime = "loraConfirmedTime";
    String loraConfirmedOperatorId = "loraConfirmedOperatorId";
    String loraConfirmedOperatorNick = "loraConfirmedOperatorNick";

    String KEY_CLOTH_NEED_BACK_IMG = "backPhotoNeeded";
    String KEY_CLOTH_STYLE_TYPE = "femaleCloth";

    String KEY_COLOR_DESCRIPTIONS = "colorDescriptions";
    String KEY_AGE_RANGE = "ageRange";
    String KEY_GENDER_TYPE = "genderType";
    String KEY_RACE = "race";

    String IS_TEST_PLAN_CREATIVE = "isTestPlanCreative";

    String KEY_USER_POINT_RETURN_FLAG = "userPointReturnFlag";

    /** 服装lora训练审核不通过后，重新审核通过，审核通过后重新扣点成功标记 */
    String KEY_USER_POINT_DEDUCT_FLAG = "userPointDeductFlag";

    String KEY_FEATURES = "features";

    String KEY_FACE_CFG = "faceCfg";

    /** 缪斯点膨胀系数，1muse = 1元人民币 = 1000point，数据库里数据按1000倍放大存储 */
    int MUSE_POINT_COEFFICIENT = 1000;

    // musePoint -> givePoint 转换系数
    BigDecimal MUSE_TO_GIVE_COEFFICIENT = BigDecimal.valueOf(2.5);

    /** 模型消耗的点数=40缪斯点*膨胀系数 */
    // 自传单色
    int CLOTH_MODEL_CONSUME_POINT = 40 * MUSE_POINT_COEFFICIENT;

    String KEY_OPEN_SCOPE = "openScope";
    String ALL = "ALL";

    String clothStyleImgs = "clothStyleImgs";
    String faces = "faces";
    String clothStyles = "clothStyles";
    String KEY_CLOTH_COLOR = "clothColor";
    String logoLocation = "logoLocation";

    // 代理拍摄和上传
    String uploadByAgentId = "uploadByAgentId";
    String uploadByAgentName = "uploadByAgentName";
    String uploadByAgentMasterId = "uploadByAgentMasterId";
    String uploadByAgentMasterName = "uploadByAgentMasterName";
    String uploadByAgentRole = "uploadByAgentRole";

    // 原始服装搭配描述（一句自然语言描述的prompt，由gpt生成的，兼容保留）
    @Deprecated
    String features = "features";
    // 用户输入的特征进行json解析后的搭配特征，中英文，json object
    String userPreferFeatures = "userPreferFeatures";
    // AI生成的结构化的搭配建议，纯英文，json object
    String aiGenFeatures = "aiGenFeatures";

    // 多色服装
    String multiColors = "multiColors";

    String materialType = "materialType";

    String female = "female";
    String male = "male";
    String child = "child";
    String unisex = "unisex";
    String upperGarment = "upper garment";
    String lowerGarment = "lower garment";
    String outfit = "outfit";

    String man = "man";
    String woman = "woman";

    String opsMemo = "opsMemo";

    String disableReason = "disableReason";
    String disableOperatorNick = "disableOperatorNick";
    String disableOperatorId = "disableOperatorId";
    String disableTime = "disableTime";
    String refundMusePoint = "refundMusePoint";
    String needRefundMusePoint = "needRefundMusePoint";
    String REFUND_MUSE_POINT_COMPLETED =  "refundMusePointCompleted";
    /** 服装重新审核通过后，进行扣点 */
    String deductMusePoint = "deductMusePoint";
    String needDeductMusePoint = "needDeductMusePoint";

    String actualSwapFaceImg = "actualSwapFaceImg";
    String actualSwapFaceImgPath = "actualSwapFaceImgPath";

    String KEY_SERVER_URL = "serverUrl";

    /** 教程图片 */
    String TUTORIAL_IMAGE = "tutorialImage";
    String KEY_SERVER_ID = "serverId";
    String KEY_TARGET_IMAGE = "targetImage";
    String KEY_RESCALE_FACTOR = "rescaleFactor";

    String KEY_RELATED_OPERATOR = "relatedOperator";

    String KEY_PURE_RGB = "pureRgb";
    String KEY_IS_PURE_COLOR = "isPureColor";
    String KEY_ORIGIN_CLOTH_COLLOCATION = "originClothCollocation";
    String KEY_TRANS_CLOTH_COLLOCATION = "transClothCollocation";

    String KEY_ORIGIN_CUSTOM_SCENE = "originCustomScene";
    String KEY_TRANS_CUSTOM_SCENE = "transCustomScene";

    String KEY_ORIGIN_REDRAW_DESC = "originRedrawDesc";
    String KEY_TRANS_REDRAW_DESC = "transRedrawDesc";

    String KEY_ORIGIN_MASK_MODEL = "originMaskModel";
    String KEY_MERGED_MASK_MODEL = "mergedMaskModel";

    String KEY_CLOTH_TYPE_CONFIGS = "clothTypeConfigs";
    String KEY_INCLUDES_BRA = "includesBra";
    String CLOTH_STYLE_TYPE = "clothStyleType";
    String MULTI_COLORS = "multiColors";

    String KEY_CAMERA_ANGLE = "cameraAngle";
    String KEY_EXT_TAGS = "extTags";
    String KEY_TAGS = "tags";
    String KEY_NEGATIVE = "negative";

    /** 图片扩图 */
    String TARGET_SIZE = "targetSize";
    String ORIGIN_POSITION = "originPosition";
    String ORIGIN_IMAGE_NAME = "originImageName";
    String TARGET_IMAGE =  "targetImage";
    String OUT_PUT_PATH = "outPutPath";
    String FILE_NAME_PREFIX = "fileNamePrefix";
    /** 创作图片来源是否是上传 */
    String KEY_IS_CREATIVE_UPLOAD = "isCreativeUpload";

    String KEY_VIDEO = "video";
    String KEY_VIDEO_URL = "videoUrl";
    String KEY_VIDEO_URLS = "videoUrls";
    String KEY_INDEX = "index";
    String KEY_RELATED = "related_";
    String KEY_TEMP_VIDEO = "temp_video_";
    String KEY_TEMP_VIDEO_TASK = "temp_video_task_";

    String SDXL = "sdxl";
    String FLUX = "flux";

    String KEY_VERSION = "version";
    String KEY_OP_VERSION = "opVersion";

    String KEY_ENABLE_ANTI_BLUR_LORA = "enableAntiBlurLora";
    String KEY_ENABLE_NEW_MODEL = "enableNewModel";

    /** 交付时间 */
    String KEY_DELIVERY_TIME = "deliveryTime";
    String KEY_DELIVERY_OPERATOR = "deliveryOperator";

    String KEY_CLOTH_MARK = "clothMark";
    String markedColor = "markedColor";

    String KEY_EXPRESSION = "expression";
    String KEY_USE_SCENE_ACCESSORIES = "useSceneAccessories";
    String KEY_HAIRSTYLE = "hairstyle";

    String KEY_REFINE_STATUS = "refineStatus";

    String KEY_MODEL_ID = "modelId";

    String DEFAULT_IMAGE = "https://aigc-platform-online.oss-cn-zhangjiakou.aliyuncs"
                           + ".com/202410/100001/prod_fix_a837a3ba25264ad7b83984d9b7ad3aff"
                           + ".png?Expires=3304893385&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature"
                           + "=EklkRVPC3JJB0o0nGbKM9%2FeIeIw"
                           + "%3D&x-oss-process=image%2Fresize%2Cm_lfit%2Cw_400%2Fquality%2Cq_99%2Fformat%2Cjpg";

    String KEY_IS_SEND_TEST_EVENT = "isSendTestEvent";

    /** 测试图片数量 */
    String KEY_TEST_IMAGE_CNT = "testImageCnt";

    /** 测试用例ID */
    String KEY_TEST_CASE_ID = "testCaseId";

    /** 测试图片生成完毕 */
    String KEY_TEST_IMAGE_FINISHED = "testImageFinished";
    /** 已发送自动创作事件 */
    String KEY_SEND_AUTO_CREATE_IMAGE = "sendAutoCreateImage";

    /** 换脸类型 */
    String KEY_SWAP_TYPE = "swapType";

    /** 最大输入的图片尺寸 */
    int MAX_INPUT_IMAGE_SIZE = 2100;

    // reactor/instantId
    String KEY_SWAP_MODEL_TYPE = "swapModelType";

    String KEY_FACE_LORA = "faceLora";
    String KEY_FACE_LORA_STRENGTH = "faceLoraStrength";
    String KEY_LORA_SWAP_FACE = "loraSwapFace";
    /** 修脸类型 */
    String KEY_REPAIR_FACE_TYPE = "repairFaceType";
    String KEY_REPAIR_AFTER_SWAP = "repairAfterSwap";

    /** 服装颜色明细 */
    String KEY_CLOTH_COLOR_DETAIL = "clothColorDetail";
    /** 服装颜色展示图片列表 */
    String KEY_CLOTH_COLOR_IMAGES = "clothColorImages";
    /** 服装颜色索引 */
    String KEY_COLOR_INDEX = "colorIndex";

    Integer MAX_COLOR_NUM = 3;
    String KEY_SEED = "seed";
    String KEY_ASSIGN_OPERATOR = "assignOperator";
    String KEY_IS_LORA = "isLora";
    String KEY_LORA_PATH = "loraPath";
    String KEY_SUB_LORA_NAMES = "subLoraNames";

    /** 服装lora路径 */
    String KEY_CLOTH_LORA_PATH = "clothLoraPath";
    /** 服装lora路径列表，for主模型 */
    String KEY_CLOTH_LORA_PATHS = "clothLoraPaths";

    /** 场景的默认lora：去背景模糊lora */
    String KEY_DEFAULT_SCENE_LORA = "open_lora/FLUX-dev-lora-AntiBlur.safetensors";

    String KEY_CUSTOMER_CASE = "customerCase";

    String KEY_AUTO_DELIVERY = "autoDelivery";

    String CREATIVE_ELEMENT_ID = "creativeElementId";
    String KEY_GARMENT_TYPE = "garmentType";
    String KEY_CLOTH_TYPE = "clothType";
    String KEY_EXPERIMENTAL = "experimental";

    // 随机
    String TEST_IMG_MODE_RANDOM = "random";
    // 笛卡尔积
    String TEST_IMG_MODE_CARTESIAN_PRO = "cartesian_product";
    String KEY_ORIGIN_REMOTE_IMAGE_NAME = "originRemoteImageName";
    String KEY_IS_PURE_BG = "isPureBG";

    String KEY_TEST_GROUP_ID = "testGroupId";
    String KEY_TEST_PARAMS = "testParams";
    String KEY_TEST_CONTEXT = "testContext";
    String KEY_TEST_ROUND_ID = "testRoundId";
    String KEY_TEST_CASE_ITEM_ID = "testCaseItemId";

    String KEY_SCENE_ID = "sceneId";
    String KEY_JSON = "json";
    String KEY_TEXT = "text";

    String KEY_POSTURE = "posture";
    String KEY_LENS = "lens";
    /** 摄影风格 */
    String KEY_STYLE = "style";
    String KEY_STYLE_OUTFIT = "styleOutfit";
    String KEY_STYLE_MODEL = "styleModel";
    String KEY_STYLE_SCENE_ID = "styleSceneId";
    String KEY_STYLE_IMAGE = "styleImage";
    String KEY_MODEL_FINISHED = "modelFinished";
    String KEY_IS_STYLE_SCENE = "isStyleScene";

    String KEY_ANALYSIS_JSON = "analysisJson";

    String KEY_BAD_IMAGE_TIMES = "badImageTimes";

    String KEY_FACE_AFTER = "faceAfter";
    String KEY_FACE_AFTER_STRENGTH = "faceAfterStrength";
    String KEY_LORA_STRENGTH = "loraStrength";
    String KEY_NOSHOW_FACE = "noshowFace";

    /** 是否不需要扣点 */
    String KEY_WITHOUT_DEDUCTION = "withoutDeduction";

    /** 使用发型 */
    String USE_HAIRSTYLE = "useHairstyle";

    /** 展示图片 */
    String SHOW_IMAGE = "showImage";
    // 展示图片列表
    String SHOW_IMGS = "showImgs";

    // 场景的服装风格
    String KEY_SCENE_CLOTH_TYPE_SCOPE = "clothTypeScope";

    /** 服装类别 */
    String KEY_CLOTH_CATEGORY = "clothCategory";
    String KEY_CLOTH_CATEGORY_OTHER = "clothCategoryOther";

    /** 正面上半身 */
    String KEY_CLOTH_UPPER_BODY_FRONT_PROMPT = "A portrait of a person's upper body, no lower body visible,";
    /** 背面上半身 */
    String KEY_CLOTH_UPPER_BODY_BACK_PROMPT = "A portrait of a person's upper body, no lower body visible,";
    /** 正面/背面下半身 */
    String KEY_CLOTH_LOWER_BODY_PROMPT = "A portrait of a person's lower body, no upper body visible,";

    String KEY_LABEL_TYPE = "labelType";
    /** 是否重新训练 */
    String KEY_RETRAIN_FLAG = "retrainFlag";
    /** 自动完成 */
    String KEY_AUTO_COMPLETE = "autoComplete";

    String KEY_IS_COMPLEX = "isComplex";
    String KEY_COMPLEX_REASONS = "complexReasons";

    /** 全身/半身不限制 */
    String KEY_BODY_TYPE_UNLIMITED = "bodyTypeUnlimited";
    String KEY_UNLIMITED = "unlimited";

    String KEY_PURE_BACKGROUND = "pure background";
    String KEY_TYPE_SHOW = "show";

    /** 训练类型 */
    String KEY_TRAIN_TYPE = "trainType";

    String KEY_SAMPLER_NAME = "samplerName";
    String KEY_SCHEDULE_NAME = "scheduleName";

    String SERVICE_TYPE_TRAIN = "TRAIN";
    String SERVICE_TYPE_CREATIVE = "CREATIVE";

    String KEY_INSTANT_ID_V2 = "instantIdV2";

    //=======================  数据分析 Map 集合 ===========================
    // 统计结果
    String TOTAL_MAP = "totalMap";
    // 对照组
    String CONTROL_GROUP_MAP = "controlGroupMap";
    // 实验组
    String EXPERIMENTAL_GROUP_MAP = "experimentalGroupMap";

    String tryonPersonImgName = "tryonPersonImgName";
    String tryonGarmentImgName = "tryonGarmentImgName";
    String cutoutKeyword = "cutoutKeyword";

    String pictureMattingImgName = "pictureMattingImgName";

    String TRYON_TYPE_ALIYUN = "aliyun";
    String TRYON_TYPE_COMFYUI = "comfyui";

    String KEY_BIZTAG = "bizTag";
    String KEY_DEMO_TAG = "demoTag";

    String KEY_AGE_DESC = "ageDesc";

    //=======================  MaterialModel 扩展信息Key ===========================
    String KEY_IRONING_CLOTH = "ironingCloth";
    String KEY_AUTO_TRAIN = "autoTrain";
    String colorNumber = "colorNumber";
    String clothStyleType = "clothStyleType";
    String highResModelShowImgUrl = "highResModelShowImgUrl";
    String IS_MANUAL_REPLACEMENT = "isManualReplacement";
    String IS_LORA_SYSTEM_RELOAD = "isLoraSystemReload";

    String KEY_LORA_MD5 = "loraMd5";
    String KEY_ORIGIN_MODEL = "originModel";
    String KEY_IS_MODIFY_LABEL = "isModifyLabel";
    String KEY_IS_MODIFY_CUTOUT = "isModifyCutout";
    String KEY_IS_MODIFY_PROMPT = "isModifyPrompt";
    String KEY_MODIFY_LABEL_OPERATOR = "modifyLabelOperator";
    String KEY_MODIFY_CUTOUT_OPERATOR = "modifyCutoutOperator";
    String KEY_IS_CUTOUT_AGAIN = "isCutoutAgain";
    String KEY_CUTOUT_AGAIN_OPERATOR = "cutoutAgainOperator";
    String KEY_LOOK_TYPE = "lookType";
    String KEY_SCENE_TYPE = "sceneType";
    String KEY_INCREASE_STRENGTH = "increaseStrength";
    String KEY_INCREASE_STEPS = "increaseSteps";
    String KEY_INIT_LABEL_TYPE = "initLabelType";
    String KEY_PROMPT_USER_ID = "promptUserId";
    String KEY_MEMO = "memo";
    String KEY_PROBLEM_TAGS = "problemTags";
    String NOW_SHOW_FACE_PROMPT = "The camera only shows the area below the chin";

    /** 训练使用mask+loss方案 */
    String MASK_LOSS = "maskLoss";
    /** 场景mask+loss方案的扩展信息 */
    String SCENE_MASK_LOSS_EXT = "{\"masked_area_loss_weight\": 1.0, \"unmasked_area_loss_weight\":0.0}";

    /** 风格场景兜底的表情 */
    String KEY_NEUTRAL_FACE = "Neutral face";

    /** 用途类型 */
    String KEY_USAGE_TYPE = "usageType";
    /** 用途备注 */
    String KEY_USAGE_MEMO = "usageMemo";

    String KEY_BASE_MODEL_VERSION = "baseModelVersion";
    String KEY_GUIDANCE = "guidance";

    String A_MID_PREFIX = "_A_MID_";

    //=======================  ImageCase 扩展信息Key ===========================
    String FILE_DIR = "fileDir";
    String FILE_NAME = "fileName";
    String IMG_URL = "imgUrl";
    String TEXT_CONTENT = "textContent";
    String TYPE = "type";
    String IS_NEED_UPLOAD = "isNeedUpload";

    // ===================== 用户偏好 =========================
    Integer MAX_CLOTH_COLLOCATION_NUM = 5;
    Integer MAX_CUSTOM_SCENE_NUM = 5;
    String KEY_CUSTOM_SCENE_DESC = "customSceneDesc";

    String KEY_TARGET_OSS_OBJECT_NAME = "targetOssObjectName";
    String KEY_XIU_TU_IMG_ID = "xiutuImgId";
    String KEY_RESULT_IMG_URL = "resultImgUrl";
    String KEY_RELATED_COMMON_TASK = "relatedCommonTask";

    //=======================  是否同步到服务器 Key ===========================
    String NEED = "need";
    String NOT_NEED = "notNeed";

    //=======================  AB测试类型 ===========================
    String CLOTHING_LORA_ID = "clothingLoraId";
    String TRAIN_TYPE = "trainType";
    String CREATE = "create";
    String CLONE = "clone";
    String KEY_KEEP_SEED = "keepSeed";
    String KEY_FLOW_TYPE = "flowType";
    String KEY_USE_SEED_STORE = "useSeedStore";

    String STYLE_SCENE = "style scene";

    //======================= 临时兼容 ============================
    String KEY_TEMP_ORIGIN_IMG_URL = "originImgUrl";

    String KEY_CONFIRM_CAN_DELIVER = "confirmCanDeliver";
    String KEY_LORA_UPLOAD_OSS_SUCCESS = "loraUploadOssSuccess";

    String PROFILE_SEND_MUSE_POINTS = "profileSendMusePoints";

    String PROFILE_RELATED_MERCHANT_ACCOUNT = "relatedMerchantAccount";
    String PROFILE_RELATED_DEMO_ACCOUNT = "relatedDemoAccount";

    //=======================  AB测试类型 ===========================
    String CLOTHE_IMAGE = "clotheImage";
    String MASK_IMAGE = "maskImage";
    String CLOTHE_TYPE = "clotheType";
    String KEY_CLOTH_DESC = "clothDesc";
    String REFERENCE_IMAGE = "referenceImage";
    String REFERENCE_ORIGINAL_IMAGE = "referenceOriginalImage";
    String REFERENCE_PROMPT = "referencePrompt";
    String PROMPT = "prompt";
    String IS_NEED_REPLACE_FACE = "isNeedReplaceFace";
    String IS_USE_LORA_FACE = "isUseLoraFace";
    String IS_USER_UPLOAD_REFERENCE = "isUserUploadReference";
    String STYLE_LORA_CONFIG = "styleLoraConfig";
    String STYLE_OUTFIT_INFO = "styleOutfitInfo";
    String BACK_TAGS = "backTags";
    String EXT_TAGS = "extTags";
    String PICTURE_MATTING_PROMPT = "pictureMattingPrompt";
    String KEY_FACE_IMAGE_NAME = "resultFaceImage";
    String KEY_MASK_IMAGE_NAME = "resultMaskImage";
    String IS_USE_FACE_PIC = "isUseFacePic";
    String SHOW_CLOTHES_PIC = "showClothedPic";
    String KEY_FACE_PROMPT = "facePrompt";
    // 视频退点，未开始生成任务就已经成功退点点列表
    String UNSTARTED_REFUNDED_VIDEO_LIST = "unstartedRefundedVideoList";

    //===================  基础款换衣 Key =========================
    String SHOW_CLOTHES_PIC_URL = "showClothedPicUrl";
    String MASK_IMAGE_URL = "maskImageUrl";

    //====================== 用户收藏 =============================
    String KEY_IMAGE_INDEXES = "imageIndexes";

    String KEY_EXCLUSIVE = "exclusive";

    String HAS_WATERMARK = "hasWatermark";

    //====================== 添加姿势示例图 =============================
    String POSE_SAMPLE_DIAGRAM = "poseSampleDiagram";
    String KEY_POSE_ELEMENT_ID = "keyPoseElementId";
    // =================== 消除笔 ========================
    String KEY_ERASE_BRUSH_Ali_TASK_ID = "eraseBrushAliTaskId";
    String KEY_ERASE_BRUSH_RESPONSE = "eraseBrushResponse";
    /** 图片缩放时整数倍对齐参数 */
    Integer ROUNDING_MODULUS = 8;
    // ================== 衣服去皱 =======================
    String KEY_REMOVE_WRINKLE_OSS_NAME = "removeWrinkleOssName";
    String KEY_REMOVE_WRINKLE_RESULT = "removeWrinkleResult";
    String KEY_REMOVE_WRINKLE_DEGREE = "rmDegree";

    // =================== 审核 ========================
    String KEY_INTERNAL_DISABLE_REASONS = "internalDisableReasons";
    String KEY_WORK_SCHEDULED_TIME = "workScheduledTime";
    String KEY_REVIEWER_ID = "reviewerId";

    // 当前服装序号：客户的第几套衣服
    String KEY_CLOTH_NUM = "clothNum";

    /** 数据统计关键字 */
    String KEY_BATCH_ID = "batchId";
    String KEY_COPY_SUFFIX = "_copy";
    String DOWNLOADED_IMAGE = "downloadedImgs";
    String KEY_BATCH_ID_LIST = "batchIdList";
    String KEY_NICKNAME = "nickname";
    String KEY_LOGIN_ID = "loginId";
    String KEY_MODEL_NAME = "modelName";
    String KEY_SHOW_IMAGE = "showImage";
    String KEY_MATERIAL_ID_LIST = "materialIdList";
    String KEY_AUDIT_MATERIAL_ID_LIST = "auditMaterialIdList";

    /** 销售/运营统计关键字 */
    // 活跃客户率 分子
    String CUSTOMER_ACTIVITY_RATE_MOLECULAR = "customerActivityRateMolecular";
    // 活跃客户率 分子
    String CUSTOMER_ACTIVITY_RATE_DENOMINATOR = "customerActivityRateDenominator";

    // 客户复购率 分子
    String CUSTOMER_REPURCHASE_RATE_MOLECULAR = "customerRepurchaseRateMolecular";
    // 客户复购率 分母
    String CUSTOMER_REPURCHASE_RATE_DENOMINATOR = "customerRepurchaseRateDenominator";

    // 定制模特比例 分子
    String CUSTOM_MODEL_CUSTOMERS_MOLECULAR = "customModelCustomersMolecular";
    // 定制模特比例 分母
    String CUSTOM_MODEL_CUSTOMERS_DENOMINATOR = "customModelCustomersDenominator";

    // 定制场景比例 分子
    String CUSTOM_SCENE_CUSTOMERS_MOLECULAR = "customSceneCustomersMolecular";
    // 定制场景比例 分母
    String CUSTOM_SCENE_CUSTOMERS_DENOMINATOR = "customSceneCustomersDenominator";

    // 服装返点率 分子
    String GARMENT_REBATE_RATE_MOLECULAR = "garmentRebateRateMolecular";
    // 服装返点率 分母
    String GARMENT_REBATE_RATE_DENOMINATOR = "garmentRebateRateDenominator";

    /** 总体统计关键字 */
    // 周 用户 id 列表
    String KEY_WEEKLY_USER_ID_LIST = "weeklyUserIdList";
    // 月 用户 id 列表
    String KEY_MONTH_USER_ID_LIST = "monthUserIdList";
    // 用户退点 id 列表
    String KEY_CUSTOMER_REFUND_USER_ID_MAP = "customerRefundUserIdMap";
    // 交付超时服装 id 列表
    String KEY_DELIVER_TIMEOUT_MATERIAL_ID_MAP = "deliveryTimeoutMaterialIdMap";
    // 余额预警用户 id 列表
    String KEY_BALANCE_WARNING_USER_ID_MAP = "balanceWarningUserIdMap";
    // 60天内未转换用户 id 列表
    String KEY_SIXTY_DAYS_NO_CONVERT_USER_ID_LIST = "sixtyDaysNoConvertUserIdList";

    /** 固定姿势创作常量 */
    String REFERENCE_INFO_LIST = "referenceInfoList";
    String KEY_IS_FIXED_POSE = "isFixedPose";

    String ORIGINAL_IMAGE_URL = "originalImageUrl";

    /** 当前姿势 id */
    String CURRENT_POSE_ID = "currentPoseId";

    String NONE = "NONE";

    /** 原创作批次信息 */
    String KEY_ORIGINAL_BATCH_INFO = "originalBatchInfo";

    String MUSE_POINT = "musePoint";
    String GAVE_IMG_COUNT = "creativeImgCountGave";

    // =================== 订单结算相关 ===================
    /** 销售的类型, 目前分为：1. 直营, 2. 外部代理 */
    String KEY_SALES_TYPE = "salesType";
    /** 是否测试账号 */
    String KEY_IS_TEST = "isTest";
    /** 默认的新客户、续费客户结算周期 */
    Integer DEFAULT_NEW_RENEWED_PERIOD = 180;
    /** 是否是新订单 */
    String KEY_IS_RENEW_ORDER = "renewedOrder";
    /** 订单被抽成对象是否处于签约首年 */
    String KEY_IS_INIT_YEAR = "initYear";
    /** 订单结算配置 */
    String KEY_ORDER_SETTLE_CONFIG = "orderSettleConfig";
    /** 默认的新客户、续费客户结算金额标准 */
    BigDecimal DEFAULT_NEW_RENEWED_STANDARD = BigDecimal.valueOf(3999.00);
    /** 渠道结算开始日期, 包含, yyyy-MM-dd */
    String KEY_SETTLE_START_DATE = "settleStartDate";
    /** 渠道结算结束日期, 包含, yyyy-MM-dd */
    String KEY_SETTLE_END_DATE = "settleEndDate";
    String KEY_OLD_SETTLE_CONFIG = "oldSettleConfig";
    String KEY_NEW_SETTLE_CONFIG = "newSettleConfig";

    // ================= 考核相关 ======================
    /** 提醒开始时间 */
    String KEY_REMIND_START_DATE = "remindStartDate";
    /** 默认提醒时长 */
    Integer DEFAULT_REMIND_DURATION = 3;
    /** 默认考核订单下限 */
    BigDecimal DEFAULT_ASSESSMENT_ORDER_LOWER_LIMIT = BigDecimal.valueOf(0);

    // ================ 主体信息 key ====================
    /** 结算配置 */
    String KEY_SETTLE_CONFIG = "SETTLE_CONFIG";
    /** 合同信息 */
    String KEY_CONTRACT_INFO = "CONTRACT_INFO";

    String KEY_PAY_TYPE = "payType";
    String KEY_CODE_URL = "codeUrl";

    /** 是否设置姿势图片 */
    String IS_SET_POSTURE = "isSetPosture";
    String IMAGE_COUNT = "imageCount";

    /** 风格类型 */
    String KEY_STYLE_TYPE = "styleType";
    /** 是否设置风格类型 */
    String KEY_IS_SET_STYLE_TYPE = "isSetStyleType";
    /** 是否需要所有服装描述 */
    String IS_NEED_ALL_SCENE_OUTFIT = "isNeedAllSceneOutfit";

    // ================ 用户额外配置 key ====================
    String KEY_USER_ADDITIONAL_INFO = "userAdditionalInfo";

    /** 图片设备信息key */
    String KEY_PICTURE_DEVICE_INFO = "pictureDeviceInfo";
    /** 是否为随机设备 */
    String KEY_IS_RANDOM_DEVICE = "isRandomDevice";

    // =============== 需要给Task标上顺序 ==================
    String KEY_TASK_ORDER = "taskOrder";
    /** 请求次数, 从 0 开始 */
    String KEY_REQUEST_FREQUENCY = "requestFrequency";
    /** 请求次数列表 */
    String KEY_REQUEST_FREQUENCIES = "requestFrequencies";
    /** 当前请求的序号, 从 0 开始 */
    String KEY_REQUEST_ORDER = "requestOrder";

    // ================ 多阶段任务常量 ==================
    // 是否是多流程任务
    String KEY_IS_MULTI_PROCESS = "isMultiProcess";
    // 是否需要保存结果信息（该标识的也用于判断该任务是否需要统计进入 batch）
    String KEY_IS_NEED_STORAGE_RESULT = "isNeedStorageResult";
    // 是否是三方任务
    String KEY_IS_THIRD_PART_TASK = "isThirdPartTask";
    // 是否已经更新完成
    String KEY_PREVIEW_IS_SYNC_SUCCESS = "previewIsSyncSuccess";
    // 参考图是否上传生成
    String KEY_PREFERENCE_IS_UPLOAD_SUCCESS = "preferenceIsUploadSuccess";
    // 参考图 ModelId
    String KEY_PREFERENCE_MODEL_ID = "preferenceModelId";
    // 是否是主 Task
    String KEY_IS_MAIN_TASK = "isMainTask";
    // 关联主TaskId
    String KEY_MAIN_TASK_ID = "mainTaskId";
    // 前置任务结果key，该 key 设置在具体任务的扩展信息中，用于前置任务结果设置
    String KEY_PRE_TASK_RESULT_KEY = "preTaskResultKey";
    // 前置任务结果 Key 的结果长度（为 1 则只取结果集合中的第一张图片）
    String KEY_PRE_TASK_RESULT_SIZE = "preTaskResultSize";
    // 前置任务结果字段
    String KEY_PRE_TASK_RESULT_LIST = "preTaskResultList";
    // 是否需要将结果转换为 ComfyUI 相对路径
    String KEY_IS_NEED_CHANGE_TO_COMFYUI_PATH = "isNeedChangeToComfyUiPath";
    // 是否已经上传了绘蛙参考图（若未上传则会在同步结果时重新上传）
    String IS_UPLOAD_HUIWA_REFERENCE = "isUploadHuiwaReference";
    // 需要完成的任务的数量
    String FINISH_TASK_COUNT = "finishTaskCount";

    // ================ 三方应用服务常量 ==================
    // 绘蛙图片质量
    String HUI_WA_PIC_QUALITY = "activePicQuality";

    //销售配置地区
    String KEY_DISTRIBUTOR_REGION_ASSIGNMENT = "DISTRIBUTOR_REGION_ASSIGNMENT";
    //交付工程师配置地区
    String KEY_ADMIN_REGION_ASSIGNMENT = "ADMIN_REGION_ASSIGNMENT";

    // 用户参考图来源
    String UPLOAD = "upload";
    String HISTORY = "history";

    String OUTPUT_PREFIX = "output";
    String FILTER_GRAPH_OUTPUT_SUFFIX = "filter_graph";
    String KEY_FRAME = "frame";
    String KEY_SECOND = "second";
    String KEY_ORIGINAL_RESULTS = "originalResults";
    String KEY_LOW_QUALITY_RESULTS = "lowQualityResults";

}

package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * CaptionUserQuery
 *
 * @version CaptionUserService.java v 0.1 2025-08-12 12:55:30
 */
@Data
public class CaptionUserQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** 用户唯一标识符（主键） */
        private Integer id;

        /** 用户登录名（唯一） */
        private String username;

        private String usernameLike;

        /** 用户密码的哈希值（加密存储） */
        private String passwordHash;

        /** 用户显示昵称 */
        private String nickname;

        /** 用户显示昵称 */
        private String nicknameLike;

        /** 用户角色标识（如：admin/user/guest） */
        private String role;

        /** 记录创建时间（自动生成） */
        private Date createTime;

        /** 记录最后更新时间（自动更新） */
        private Date modifyTime;

        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}
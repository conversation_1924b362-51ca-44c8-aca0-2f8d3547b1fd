/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 积分流水类型枚举
 *
 * <AUTHOR>
 * @version : PointLogTypeEnum.java, v 0.1 2024/6/21 11:19 renxiao.wu Exp $
 */
@Getter
public enum PointLogTypeEnum {
    REGISTER_GIVE("REGISTER_GIVE", "注册赠送", null),

    RECHARGE("RECHARGE", "充值", null),

    RECHARGE_MANUAL("RECHARGE_MANUAL", "人工充值", null),

    ADJUST_MANUAL("ADJUST_MANUAL", "人工调整", null),

    LORA_TRAIN("LORA_TRAIN", "Lora训练", null),

    LORA_TRAIN_RETURN("LORA_TRAIN_RETURN", "Lora训练退回", null),

    LORA_TRAIN_DEDUCT("LORA_TRAIN_DEDUCT","Lora训练重新审核通过并扣点",null),

    EXPERIENCE_CREATE("EXPERIENCE_CREATE", "体验创作", "免费试用"),

    EXPERIENCE_RETURN("EXPERIENCE_RETURN", "体验退回", null),

    CREATIVE_CREATE("CREATIVE_CREATE", "图片创作", null),

    CREATIVE_RETURN("CREATIVE_RETURN", "创作退回", null),

    LOGO_COMBINE_CREATE("LOGO_COMBINE_CREATE", "印花上身创作", "印花上身"),

    REMOVE_WRINKLE_CREATE("REMOVE_WRINKLE_CREATE", "衣服去皱", "衣服去皱"),

    LOGO_COMBINE_RETURN("CREATIVE_RETURN", "印花上身退回", null),

    IMAGE_UPSCALE_CREATE("IMAGE_UPSCALE_CREATE", "2倍放大创作", "2倍放大"),

    IMAGE_UPSCALE_RETURN("IMAGE_UPSCALE_RETURN", "2倍放大退回", null),

    CREATE_VIDEO_CREATE("CREATE_VIDEO_CREATE", "视频创作", "图生视频"),

    CREATE_VIDEO_RETURN("CREATE_VIDEO_RETURN", "视频创作退回", null),

    FACE_SCENE_SWITCH_CREATE("FACE_SCENE_SWITCH_CREATE", "换头换背景", "换头换背景"),

    PARTIAL_REDRAW_CREATE("PARTIAL_REDRAW_CREATE", "局部重绘", "局部重绘"),

    REPAIR_DETAIL_CREATE("REPAIR_DETAIL_CREATE", "细节修补", "细节修补"),

    ERASE_BRUSH_CREATE("ERASE_BRUSH_CREATE", "消除笔", "消除笔"),

    REPAIR_HANDS_CREATE("REPAIR_HANDS_CREATE", "手部修复", "手部修复"),

    BASIC_CHANGING_CLOTHES("BASIC_CHANGING_CLOTHES", "基础款换衣", "基础款换衣"),

    BASIC_CHANGING_CLOTHES_RETURN("BASIC_CHANGING_CLOTHES_RETURN", "基础款换衣退款", "基础款换衣退款"),

    IMAGE_EXPAND_CREATE("IMAGE_EXPAND_CREATE","图片扩图","图片扩图"),

    IMAGE_EXPAND_RETURN("IMAGE_EXPAND_RETURN","图片扩图","图片扩图"),

    HUIWA_BASIC_CHANGING_CLOTHES("HUIWA_BASIC_CHANGING_CLOTHES", "绘蛙基础款换衣", "绘蛙基础款换衣"),

    POSE_SAMPLE_DIAGRAM("POSE_SAMPLE_DIAGRAM", "姿势示例图", "姿势示例图"),

    FIXED_POSTURE_CREATION_CREATE("FIXED_POSTURE_CREATION_CREATE", "固定姿势创作", "固定姿势创作"),

    FIXED_POSTURE_CREATION_RETURN("FIXED_POSTURE_CREATION_RETURN", "固定姿势创作退回", null),

    BRAND_TRY_ON_CREATE("BRAND_TRY_ON_CREATE", "使用细节修补流程大牌上身", "大牌上身"),

    BRAND_TRY_ON_RETURN("BRAND_TRY_ON_RETURN", "大牌上身退回", null),

    CLOTH_RECOLOR("CLOTH_RECOLOR", "服装换色", "服装换色"),

    REMOVE_WRINKLE_4_DC_CREATE("REMOVE_WRINKLE_4_DC_CREATE", "羽绒服去皱", "羽绒服去皱"),

    REMOVE_WRINKLE_4_DC_RETURN("REMOVE_WRINKLE_4_DC_RETURN", "羽绒服去皱退回", "羽绒服去皱退回"),

    ERASE_BRUSH_V2_CREATE("ERASE_BRUSH_V2_CREATE", "消除笔V2", "消除笔V2"),

    ERASE_BRUSH_V2_RETURN("ERASE_BRUSH_V2_RETURN", "消除笔V2退回", "消除笔V2退回"),

    ;

    /** 枚举码 */
    private String code;

    /** 枚举描述 */
    private String desc;

    private String title;

    private PointLogTypeEnum(String code, String desc, String title) {
        this.code = code;
        this.desc = desc;
        this.title = title;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static PointLogTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (PointLogTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}

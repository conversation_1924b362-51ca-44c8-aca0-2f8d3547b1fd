package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageUploadReq;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Image控制器
 *
 * <AUTHOR>
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/images")
public class ImageController {

	/** imageService */
	@Autowired
	private ImageService imageService;
    @Autowired
	private ImageCaptionUserService imageCaptionUserService;

    @Autowired
    private MaterialModelService materialModelService;

    @PostMapping("/fixClothUrl")
    public Result<?> fixClothUrl() {
        ImageQuery query = new ImageQuery();
        query.setType("cloth");

        List<ImageVO> imgs = imageService.queryImageList(query);
        for (ImageVO img : imgs) {
            if (img.getMetadata() != null && img.getMetadata().containsKey("materialModelId")) {
                Integer clothId = img.getMetadata().getInteger("materialModelId");
                if (clothId != null && img.getUrl().contains("resize")) {
                    String newUrl = materialModelService.queryDetailShowImage(clothId);
                    if (StringUtils.isNotBlank(newUrl)) {

                        ImageVO update = new ImageVO();
                        update.setId(img.getId());
                        update.setUrl(newUrl);

                        log.info("update image id: {}, url: {} -> {}", img.getId(), img.getUrl(), newUrl);
                        imageService.updateByIdSelective(update);
                    }
                }
            }
        }

        return Result.success();
    }

    @GetMapping("/getById/{id}")
	public Result<ImageVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageService.selectById(id));
	}
	
	@PostMapping("/updateClothType")
	public Result<Boolean> updateClothType(@Valid @RequestBody ImageQuery query){
        imageService.updateClothType(query);
		return Result.success(true);
	}

	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ImageVO image){
		return Result.success(imageService.insert(image).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody ImageVO image){
		imageService.updateByIdSelective(image);
		return Result.success();
	}

	@PostMapping("/deepUpdateById")
	public Result<?> deepUpdateById(@Valid @RequestBody ImageVO image){
		imageService.deepUpdateById(image);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<ImageVO>> queryImageList(@Valid @RequestBody ImageQuery query){
		return Result.success(imageService.queryImageList(query));
	}

	@PostMapping("/queryTags")
	public Result<List<String>> queryImageTags(@Valid @RequestBody ImageQuery query){
		return Result.success(imageService.queryImageTags(query));
	}

	@PostMapping("/queryByPage")
	public Result<PageInfo<ImageVO>> getImageByPage(@Valid @RequestBody ImageQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(imageService.queryImageByPage(query));
	}

	@PostMapping("/queryBatchCount")
	public Result<Map<String, Long>> queryBatchCount(@Valid @RequestBody ImageQuery query) {
        if (query == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "请求参数非法");
		}
		return Result.success(imageService.queryBatchCount(query));
	}

	@PostMapping("/queryFieldValues")
	public Result<Map<String, String[]>> queryFieldValues(@Valid @RequestBody ImageQuery query) {
        if (query == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "请求参数非法");
		}
		return Result.success(imageService.queryFieldValues(query));
	}

    @PostMapping("/updateImageHash")
	public Result<ImageVO> updateImageHash(@Valid @RequestBody ImageQuery query) {
        imageService.updateImageHash(query);
        return Result.success();
	}

    @PostMapping("/upload")
	public Result<ImageVO> upload(@Valid @RequestBody ImageUploadReq req) {
        ImageVO imageVO = imageService.upload(req);
        imageCaptionUserService.saveFromImage(imageVO.getId(), req.getCaptionType(), req.getCaption());
        return Result.success(imageVO);
	}

	@GetMapping("/countByIntendedUse")
	public Result<Map<String, Integer>> countByIntendedUse() {
		return Result.success(imageService.countByIntendedUse());
	}
}

package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.TestGroupTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ComfyuiTplInfo;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.TestPlanQuery;
import ai.conrain.aigc.platform.service.model.request.AddTestPlanRequest;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.CreativeUtils;
import ai.conrain.aigc.platform.service.util.EasyExcelUtils;
import ai.conrain.aigc.platform.service.util.PromptUtils;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**
 * TestPlan控制器
 *
 * <AUTHOR>
 * @version TestPlanService.java v 0.1 2024-12-19 01:24:06
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testPlan")
public class TestPlanController {
    @Autowired
    private TestPlanService testPlanService;
    @Autowired
    private TestItemService testItemService;
    @Autowired
    private TestItemGroupService testItemGroupService;
    @Autowired
    private TestResultService testResultService;
    @Autowired
    private CreativeTaskService creativeTaskService;

    @GetMapping("/getById/{id}")
    public Result<TestPlanVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(testPlanService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody AddTestPlanRequest request) {
        try {
            TestPlanVO data = testPlanService.create(request);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加AB测试计划失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建AB测试计划失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        testPlanService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/deleteTestItemById/{testItemId}")
    public Result<?> deleteTestItemById(@PathVariable Integer testItemId) {
        testPlanService.deleteTestItemById(testItemId);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody TestPlanVO testPlan) {
        testPlanService.updateByIdSelective(testPlan);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<TestPlanVO>> queryTestPlanList(@Valid @RequestBody TestPlanQuery query) {
        return Result.success(testPlanService.queryTestPlanList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<TestPlanVO>> getTestPlanByPage(@Valid @RequestBody TestPlanQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }
        return Result.success(testPlanService.queryTestPlanByPage(query));
    }

    @PostMapping("/queryTestResult")
    public Result<List<TestItemGroupVO>> queryTestResult(@NotNull @JsonArg Integer itemId) {
        return Result.success(testItemGroupService.queryWithResultByItemId(itemId));
    }

    /**
     * 打分
     *
     * @param resultId 结果 id
     * @param isDraw   是否平局（平局则都设置为 true）
     */
    @PostMapping("/scoring")
    public Result<?> scoring(@NotNull @JsonArg Integer resultId, @JsonArg Boolean isDraw, @JsonArg Boolean score) {
        if (score == null) {
            score = true;
        }
        testPlanService.scoring(resultId, isDraw, score);
        return Result.success();
    }

    @PostMapping("/enable")
    public Result<?> enable(@NotNull @JsonArg Integer id, @NotNull @JsonArg Boolean enable) {
        testPlanService.enable(id, enable);
        return Result.success();
    }

    @PostMapping("/begin")
    public Result<?> begin(@NotNull @JsonArg Integer planId, @JsonArg Integer itemId) throws IOException {
        testPlanService.begin(planId, itemId);
        return Result.success();
    }

    @PostMapping("/finish")
    public Result<?> finish(@NotNull @JsonArg Integer id) {
        testPlanService.finish(id);
        return Result.success();
    }

    /**
     * 获取指定测试计划实验项的 BadCase 比例
     *
     * @param id   测试项 Id（或其他 Id）
     * @param type 分析结果类型
     * @return {@link AnalysisRatioVO} BadCase 比例
     */
    @PostMapping("/resultAnalysisRatio")
    public Result<ResultAnalysisRatioVO> resultAnalysisRatio(@NotNull @JsonArg Integer id,
                                                             @NotNull @JsonArg String type) {
        return Result.success(testResultService.resultAnalysisRatio(id, type));
    }

    /**
     * 获取指定测试计划实验项的 BadCase 比例
     *
     * @param id 测试计划 Id
     * @return {@link AnalysisRatioVO} 打分比例数据
     */
    @PostMapping("/extInfoAnalysisRatio")
    public Result<ExtInfoAnalysisRatioVO> extInfoAnalysisRatio(@NotNull @JsonArg Integer id) {
        return Result.success(testPlanService.extInfoAnalysisRatio(id));
    }

    /**
     * 更新测试结果扩展信息
     *
     * @param req 请求
     * @return 是否更新成功
     */
    @PostMapping("/updateExtInfo")
    public Result<Boolean> updateExtInfo(@Valid @RequestBody ExtInfoReq req) {
        return Result.success(testResultService.updateExtInfo(req));
    }

    @PostMapping("/exportItemsParams")
    public void exportItemsParams(@NotNull @JsonArg String ids, HttpServletResponse response) throws IOException {
        String[] split = ids.split(",");

        // 准备数据
        List<List<String>> data = new ArrayList<>();
        //服装和模特的lora地址
        data.add(Arrays.asList("test_item_id", "test_result_round", "scene_image", "prompt", "width", "height",
            "result_image_url", "cloth_lora_path", "model_lora_path"));

        for (String s : split) {
            int itemId = Integer.parseInt(s);
            TestItemVO item = testItemService.selectById(itemId);
            List<TestResultVO> results = testResultService.queryByItemIdAndType(item.getId(),
                TestGroupTypeEnum.EXPERIMENTAL);
            results.forEach(result -> {
                CreativeTaskVO task = creativeTaskService.selectFullById(result.getTaskId());
                String[] proportions = CreativeUtils.getImageSizeFromProportion(task.getImageProportion());

                ComfyuiTplInfo tplInfo = task.getTplInfo();
                @SuppressWarnings("unchecked")
                CreativeElementVO face = new JSONObject(
                    (Map<String, Object>)tplInfo.getTplParams().get("FACE")).toJavaObject(CreativeElementVO.class);
                String faceLoraPath = face.getExtInfo(KEY_FACE_LORA, String.class);

                @SuppressWarnings("unchecked")
                CreativeElementVO scene = new JSONObject(
                    (Map<String, Object>)tplInfo.getTplParams().get("SCENE")).toJavaObject(CreativeElementVO.class);
                String styleImage = scene.getExtInfo(KEY_STYLE_IMAGE, String.class);

                @SuppressWarnings("unchecked")
                MaterialModelVO lora = new JSONObject(
                    (Map<String, Object>)tplInfo.getTplParams().get("lora")).toJavaObject(MaterialModelVO.class);
                String modelLoraPath = lora.getLoraName();

                String prompt = lora.getTags();
                if (LabelTypeEnum.getByCode(lora.getExtInfo(KEY_LABEL_TYPE, String.class))
                    != LabelTypeEnum.STRUCTURAL) {
                    JSONObject workflow = creativeTaskService.fetchWorkflowByTask(task);
                    prompt = PromptUtils.fetchPromptFromWorkflow(workflow);
                }

                //noinspection DataFlowIssue,SequencedCollectionMethodCanBeUsed
                data.add(Arrays.asList(s, result.getRoundId() + "", styleImage, prompt, proportions[0], proportions[1],
                    task.getResultImages().get(0), modelLoraPath, faceLoraPath));

            });
        }

        EasyExcelUtils.export(data, response);
    }

    @PostMapping("/importAndUpdateExpResult")
    public Result<?> importAndUpdateExpResult(@RequestParam("file") MultipartFile file,
                                              @RequestParam("itemId") Integer itemId) throws IOException {
        if (file.isEmpty()) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "上传文件不能为空");
        }

        testPlanService.importAndUpdateExpResult(file, itemId);
        return Result.success();
    }

}
